# 💰 货币换算指南

## 🔍 你的系统计费规则

根据你提供的信息，你的New-API系统使用以下计费规则：

- **500,000 系统单位 = $1 美元**
- 当前Token余额：500,000 系统单位 = $1.00 USD

## 📊 常用金额换算表

| 美元金额 | 系统单位 | 说明 |
|---------|---------|------|
| $1.00   | 500,000 | 当前Token余额 |
| $5.00   | 2,500,000 | |
| $10.00  | 5,000,000 | |
| $20.00  | 10,000,000 | |
| $50.00  | 25,000,000 | 推荐刷新金额 |
| $100.00 | 50,000,000 | |

## ⚙️ 新配置格式

脚本现在支持直接使用美元配置，更加直观：

### 新格式（推荐）
```json
{
  "currency_settings": {
    "system_units_per_dollar": 500000,
    "currency_symbol": "$",
    "description": "500000 system units = $1 USD"
  },
  "tokens_to_refresh": [
    {
      "token_id": 16,
      "token_name": "test1",
      "target_amount_usd": 50.0,
      "description": "每日刷新到$50美元"
    }
  ]
}
```

### 旧格式（仍然支持）
```json
{
  "tokens_to_refresh": [
    {
      "token_id": 16,
      "token_name": "test1",
      "target_quota": 25000000,
      "description": "每日刷新到$50美元(25000000系统单位)"
    }
  ]
}
```

## 🎯 你的当前配置

基于你的信息，我已经创建了配置文件：

- **当前余额**: $1.00 (500,000 系统单位)
- **目标余额**: $50.00 (25,000,000 系统单位)
- **Token ID**: 16
- **Token名称**: test1

## 🧮 快速换算公式

### 美元 → 系统单位
```
系统单位 = 美元金额 × 500,000
```

### 系统单位 → 美元
```
美元金额 = 系统单位 ÷ 500,000
```

## 📝 日志显示示例

使用新脚本后，日志会同时显示美元和系统单位：

```
2025-01-20 10:30:01 - INFO - 🔄 开始处理 test1 (ID: 16) - 目标: $50.00
2025-01-20 10:30:02 - INFO - 📊 当前余额: $1.00 (500000 系统单位)
2025-01-20 10:30:02 - INFO - 📊 目标余额: $50.00 (25000000 系统单位)
2025-01-20 10:30:02 - INFO - 🧪 [干运行模式] 将会把 test1 余额从 $1.00 更新为 $50.00
```

## ⚠️ 重要提醒

1. **确认换算比例**: 请确认你的系统确实是 500,000 系统单位 = $1 USD
2. **测试先行**: 务必先使用干运行模式测试
3. **小额测试**: 建议先用小金额测试，确认换算正确
4. **备份数据**: 正式使用前建议备份数据库

## 🔧 如何修改换算比例

如果你的系统换算比例不同，请修改配置文件中的 `currency_settings`:

```json
{
  "currency_settings": {
    "system_units_per_dollar": 你的换算比例,
    "currency_symbol": "$",
    "description": "自定义说明"
  }
}
```

---

**注意**: 这个换算比例是根据你提供的信息设定的。如果实际情况不同，请及时调整配置。
