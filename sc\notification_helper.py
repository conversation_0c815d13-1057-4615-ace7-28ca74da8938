#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通知助手模块
支持企业微信、钉钉、Telegram等多种通知方式
"""

import requests
import json
import time
from datetime import datetime
from typing import Dict, List, Optional

class NotificationManager:
    """通知管理器"""

    def __init__(self, config: Dict):
        """
        初始化通知管理器

        Args:
            config: 通知配置
        """
        self.config = config
        self.notification_config = config.get('notifications', {})

        # 获取货币设置
        self.currency_settings = config.get('currency_settings', {
            "system_units_per_dollar": 500000,
            "currency_symbol": "$"
        })

    def format_currency(self, system_units: int) -> str:
        """格式化货币显示"""
        if system_units == 0:
            return f"{self.currency_settings['currency_symbol']}0.00"

        usd_amount = system_units / self.currency_settings['system_units_per_dollar']
        symbol = self.currency_settings['currency_symbol']
        return f"{symbol}{usd_amount:.2f}"
        
    def send_notification(self, title: str, content: str, status: str = "info") -> bool:
        """
        发送通知
        
        Args:
            title: 通知标题
            content: 通知内容
            status: 状态 (success, error, warning, info)
            
        Returns:
            是否发送成功
        """
        success = True
        
        # 企业微信通知
        if self.notification_config.get('wechat_work', {}).get('enabled', False):
            try:
                self._send_wechat_work(title, content, status)
            except Exception as e:
                print(f"❌ 企业微信通知发送失败: {e}")
                success = False
        
        # 钉钉通知
        if self.notification_config.get('dingtalk', {}).get('enabled', False):
            try:
                self._send_dingtalk(title, content, status)
            except Exception as e:
                print(f"❌ 钉钉通知发送失败: {e}")
                success = False
        
        # Telegram通知
        if self.notification_config.get('telegram', {}).get('enabled', False):
            try:
                self._send_telegram(title, content, status)
            except Exception as e:
                print(f"❌ Telegram通知发送失败: {e}")
                success = False
        
        # 邮件通知
        if self.notification_config.get('email', {}).get('enabled', False):
            try:
                self._send_email(title, content, status)
            except Exception as e:
                print(f"❌ 邮件通知发送失败: {e}")
                success = False
        
        return success
    
    def _send_wechat_work(self, title: str, content: str, status: str):
        """发送企业微信通知"""
        wechat_config = self.notification_config['wechat_work']
        webhook_url = wechat_config['webhook_url']
        
        # 状态图标
        status_icons = {
            'success': '✅',
            'error': '❌', 
            'warning': '⚠️',
            'info': 'ℹ️'
        }
        
        icon = status_icons.get(status, 'ℹ️')
        
        # 构建消息
        message = f"{icon} **{title}**\n\n{content}\n\n⏰ 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        data = {
            "msgtype": "markdown",
            "markdown": {
                "content": message
            }
        }
        
        # 如果配置了@人员
        if wechat_config.get('mentioned_list'):
            data["markdown"]["mentioned_list"] = wechat_config['mentioned_list']
        
        response = requests.post(webhook_url, json=data, timeout=10)
        response.raise_for_status()
        
        result = response.json()
        if result.get('errcode') != 0:
            raise Exception(f"企业微信API错误: {result.get('errmsg', '未知错误')}")
    
    def _send_dingtalk(self, title: str, content: str, status: str):
        """发送钉钉通知"""
        dingtalk_config = self.notification_config['dingtalk']
        webhook_url = dingtalk_config['webhook_url']
        
        status_icons = {
            'success': '✅',
            'error': '❌',
            'warning': '⚠️', 
            'info': 'ℹ️'
        }
        
        icon = status_icons.get(status, 'ℹ️')
        
        message = f"{icon} {title}\n\n{content}\n\n⏰ 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        data = {
            "msgtype": "text",
            "text": {
                "content": message
            }
        }
        
        # 如果配置了@人员
        if dingtalk_config.get('at_mobiles'):
            data["at"] = {
                "atMobiles": dingtalk_config['at_mobiles'],
                "isAtAll": dingtalk_config.get('at_all', False)
            }
        
        response = requests.post(webhook_url, json=data, timeout=10)
        response.raise_for_status()
        
        result = response.json()
        if result.get('errcode') != 0:
            raise Exception(f"钉钉API错误: {result.get('errmsg', '未知错误')}")
    
    def _send_telegram(self, title: str, content: str, status: str):
        """发送Telegram通知"""
        telegram_config = self.notification_config['telegram']
        bot_token = telegram_config['bot_token']
        chat_id = telegram_config['chat_id']
        
        status_icons = {
            'success': '✅',
            'error': '❌',
            'warning': '⚠️',
            'info': 'ℹ️'
        }
        
        icon = status_icons.get(status, 'ℹ️')
        
        message = f"{icon} *{title}*\n\n{content}\n\n⏰ 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
        data = {
            "chat_id": chat_id,
            "text": message,
            "parse_mode": "Markdown"
        }
        
        response = requests.post(url, json=data, timeout=10)
        response.raise_for_status()
        
        result = response.json()
        if not result.get('ok'):
            raise Exception(f"Telegram API错误: {result.get('description', '未知错误')}")
    
    def _send_email(self, title: str, content: str, status: str):
        """发送邮件通知"""
        # 这里可以实现邮件发送逻辑
        # 由于邮件配置较复杂，暂时跳过实现
        pass
    
    def send_success_notification(self, results: Dict):
        """发送成功通知"""
        title = "🎉 Token余额刷新成功"

        content_lines = [
            f"📊 **执行统计**:",
            f"• 成功: {results['success']} 个",
            f"• 失败: {results['failed']} 个",
            f"• 总计: {results['total']} 个",
            "",
            "📝 **处理详情**:"
        ]

        # 添加详细的token信息
        for token_detail in results.get('token_details', []):
            if token_detail['success']:
                name = token_detail['token_name']
                token_id = token_detail['token_id']
                daily_usage = token_detail.get('daily_usage', 0)
                final_quota = token_detail.get('final_quota', 0)

                # 格式化显示
                daily_usage_display = self.format_currency(daily_usage) if daily_usage > 0 else "$0.00"
                final_amount_display = self.format_currency(final_quota)

                if token_detail.get('dry_run', False):
                    status_icon = "🧪"
                    status_text = "[测试模式]"
                elif token_detail.get('updated', False):
                    status_icon = "✅"
                    status_text = ""
                else:
                    status_icon = "✨"
                    status_text = "[无需更新]"

                content_lines.append(
                    f"{status_icon} **{name}** (ID: {token_id}) {status_text}"
                )
                content_lines.append(
                    f"   📊 昨日用量: {daily_usage_display} | 设置后余额: {final_amount_display}"
                )

        content = "\n".join(content_lines)

        self.send_notification(title, content, "success")
    
    def send_error_notification(self, error_msg: str, results: Optional[Dict] = None):
        """发送错误通知"""
        title = "❌ Token余额刷新失败"

        content_lines = [
            f"🚨 **错误信息**: {error_msg}",
            ""
        ]

        if results:
            content_lines.extend([
                f"📊 **执行统计**:",
                f"• 成功: {results['success']} 个",
                f"• 失败: {results['failed']} 个",
                f"• 总计: {results['total']} 个",
                "",
                "📝 **详细情况**:"
            ])

            # 显示所有token的处理结果
            for token_detail in results.get('token_details', []):
                name = token_detail['token_name']
                token_id = token_detail['token_id']

                if token_detail['success']:
                    daily_usage = token_detail.get('daily_usage', 0)
                    final_quota = token_detail.get('final_quota', 0)

                    daily_usage_display = self.format_currency(daily_usage) if daily_usage > 0 else "$0.00"
                    final_amount_display = self.format_currency(final_quota)

                    content_lines.append(f"✅ **{name}** (ID: {token_id})")
                    content_lines.append(f"   📊 昨日用量: {daily_usage_display} | 设置后余额: {final_amount_display}")
                else:
                    error_info = token_detail.get('error', '未知错误')
                    content_lines.append(f"❌ **{name}** (ID: {token_id}) - {error_info}")

        content = "\n".join(content_lines)

        self.send_notification(title, content, "error")

def create_notification_config_template():
    """创建通知配置模板"""
    template = {
        "notifications": {
            "wechat_work": {
                "enabled": False,
                "webhook_url": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_KEY",
                "mentioned_list": ["@all"]  # 或者具体的用户ID列表
            },
            "dingtalk": {
                "enabled": False,
                "webhook_url": "https://oapi.dingtalk.com/robot/send?access_token=YOUR_TOKEN",
                "at_mobiles": [],  # 要@的手机号列表
                "at_all": False
            },
            "telegram": {
                "enabled": False,
                "bot_token": "YOUR_BOT_TOKEN",
                "chat_id": "YOUR_CHAT_ID"
            },
            "email": {
                "enabled": False,
                "smtp_server": "smtp.gmail.com",
                "smtp_port": 587,
                "username": "<EMAIL>",
                "password": "your_password",
                "to_emails": ["<EMAIL>"]
            }
        }
    }
    
    return template
