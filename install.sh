#!/bin/bash
# Token刷新脚本安装脚本

set -e  # 遇到错误立即退出

echo "🚀 Token刷新脚本安装程序"
echo "=" * 50

# 检查Python版本
echo "🐍 检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3未安装，请先安装Python3"
    exit 1
fi

PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
echo "✅ Python版本: $PYTHON_VERSION"

# 检查pip
echo "📦 检查pip..."
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3未安装，请先安装pip3"
    exit 1
fi
echo "✅ pip3已安装"

# 创建项目目录
PROJECT_DIR="$HOME/token-refresh"
echo "📁 创建项目目录: $PROJECT_DIR"
mkdir -p "$PROJECT_DIR/logs"
cd "$PROJECT_DIR"

# 设置文件权限
echo "🔒 设置文件权限..."
if [ -f "config.json" ]; then
    chmod 600 config.json
    echo "✅ config.json权限已设置为600"
fi

if ls *.py 1> /dev/null 2>&1; then
    chmod +x *.py
    echo "✅ Python脚本执行权限已设置"
fi

# 安装依赖
echo "📦 安装Python依赖..."
if [ -f "requirements.txt" ]; then
    echo "使用requirements.txt安装依赖..."
    pip3 install --user -r requirements.txt
else
    echo "直接安装requests库..."
    pip3 install --user requests
fi

echo "✅ 依赖安装完成"

# 测试安装
echo "🧪 测试安装..."
python3 -c "import requests; print('✅ requests库导入成功')"

# 检查必要文件
echo "📋 检查必要文件..."
REQUIRED_FILES=("token_refresh_script.py" "test_api.py" "config.json")
for file in "${REQUIRED_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file 存在"
    else
        echo "⚠️  $file 不存在，请确保已上传此文件"
    fi
done

# 运行API测试
echo "🔍 运行API连接测试..."
if [ -f "test_api.py" ] && [ -f "config.json" ]; then
    echo "执行: python3 test_api.py"
    python3 test_api.py
else
    echo "⚠️  跳过API测试，缺少必要文件"
fi

echo ""
echo "🎉 安装完成！"
echo "📍 项目位置: $PROJECT_DIR"
echo ""
echo "📋 下一步操作："
echo "1. 确保config.json配置正确"
echo "2. 运行测试: python3 test_api.py"
echo "3. 干运行测试: python3 token_refresh_script.py (确保dry_run: true)"
echo "4. 配置通知: python3 setup_notifications.py (可选)"
echo "5. 设置定时任务: crontab -e"
echo ""
echo "🔧 定时任务示例："
echo "# 每天凌晨2点执行"
echo "0 2 * * * cd $PROJECT_DIR && python3 token_refresh_script.py >> logs/cron.log 2>&1"
echo ""
echo "📊 查看日志:"
echo "tail -f $PROJECT_DIR/logs/cron.log"
