# Token余额自动刷新脚本使用说明

这是一个用于自动刷新New-API项目中Token余额的Python脚本，可以每天定时将指定的Token余额重置到设定的金额。

## ✨ 功能特点

- 🔄 **自动刷新**: 每日定时刷新指定Token的余额
- 🎯 **精确控制**: 可以为每个Token设置不同的目标余额
- 🛡️ **安全认证**: 使用项目原生的API认证方式
- 📊 **详细日志**: 完整的操作日志记录
- 🧪 **干运行模式**: 测试模式，不实际修改数据
- ⚡ **批量处理**: 支持同时处理多个Token

## 📋 使用前准备

### 1. 获取必要信息

在你的New-API管理界面中获取以下信息：

#### 获取Access Token
1. 登录你的New-API管理界面
2. 进入用户设置页面
3. 点击"生成Access Token"按钮
4. 复制生成的Access Token

#### 获取用户ID
1. 在浏览器开发者工具中查看网络请求
2. 或者通过API调用 `/api/user/self` 获取用户信息
3. 记录你的用户ID（数字）

#### 获取Token ID
1. 进入Token管理页面
2. 查看你要刷新的Token列表
3. 记录每个Token的ID（通常在URL或数据中可以看到）

### 2. 安装Python依赖

```bash
pip install requests
```

## 🚀 快速开始

### 1. 下载脚本

将 `token_refresh_script.py` 下载到你的服务器上。

### 2. 首次运行生成配置文件

```bash
python3 token_refresh_script.py
```

首次运行会自动生成 `config.json` 配置文件模板。

### 3. 编辑配置文件

编辑生成的 `config.json` 文件：

```json
{
  "api_base_url": "https://your-domain.com",
  "access_token": "your_access_token_here",
  "user_id": 123,
  "tokens_to_refresh": [
    {
      "token_id": 1,
      "token_name": "用户A的Token",
      "target_quota": 50000,
      "description": "每日刷新到50元"
    },
    {
      "token_id": 2,
      "token_name": "用户B的Token",
      "target_quota": 100000,
      "description": "每日刷新到100元"
    }
  ],
  "dry_run": false,
  "timeout": 30
}
```

#### 配置说明：

- `api_base_url`: 你的New-API服务地址
- `access_token`: 你的Access Token
- `user_id`: 你的用户ID
- `tokens_to_refresh`: 要刷新的Token列表
  - `token_id`: Token的ID
  - `token_name`: Token名称（用于日志显示）
  - `target_quota`: 目标余额（注意：这里的单位是系统内部单位，通常1元=1000）
  - `description`: 描述信息
- `dry_run`: 是否为测试模式（true=测试，false=实际执行）
- `timeout`: 请求超时时间（秒）

### 4. 测试运行

先设置 `dry_run: true` 进行测试：

```bash
python3 token_refresh_script.py
```

确认无误后，设置 `dry_run: false` 正式运行。

## ⏰ 设置定时任务

### 使用Cron设置每日0点执行

编辑crontab：

```bash
crontab -e
```

添加以下行（每天0点执行）：

```bash
0 0 * * * cd /path/to/your/script && python3 token_refresh_script.py >> cron.log 2>&1
```

### 其他时间设置示例

```bash
# 每天早上8点执行
0 8 * * * cd /path/to/your/script && python3 token_refresh_script.py

# 每天中午12点执行
0 12 * * * cd /path/to/your/script && python3 token_refresh_script.py

# 每周一早上9点执行
0 9 * * 1 cd /path/to/your/script && python3 token_refresh_script.py
```

## 📊 日志说明

脚本会生成详细的日志文件 `token_refresh.log`，包含：

- 执行时间
- 处理的Token信息
- 当前余额和目标余额
- 更新结果
- 错误信息（如果有）

示例日志：
```
2025-01-20 00:00:01 - INFO - 🚀 开始执行Token余额刷新任务
2025-01-20 00:00:01 - INFO - ⏰ 执行时间: 2025-01-20 00:00:01
2025-01-20 00:00:01 - INFO - 📝 共需要处理 2 个Token
2025-01-20 00:00:01 - INFO - 🔄 开始处理 用户A的Token (ID: 1)
2025-01-20 00:00:02 - INFO - 📊 当前余额: 25000, 目标余额: 50000
2025-01-20 00:00:03 - INFO - ✅ Token 用户A的Token (ID: 1) 余额已更新为 50000
```

## 🔧 高级配置

### 余额单位说明

New-API系统中的余额单位通常是：
- 1元 = 1000 系统单位
- 50元 = 50000 系统单位
- 100元 = 100000 系统单位

请根据你的系统实际情况调整 `target_quota` 的值。

### 批量添加Token

如果你有很多Token需要管理，可以通过脚本批量生成配置：

```python
# 生成多个Token配置的示例
tokens = []
for i in range(1, 11):  # Token ID 1-10
    tokens.append({
        "token_id": i,
        "token_name": f"Token-{i}",
        "target_quota": 50000,  # 都设置为50元
        "description": f"Token {i} 每日刷新到50元"
    })
```

## ❗ 注意事项

1. **权限确认**: 确保你的Access Token有修改Token的权限
2. **备份重要**: 建议在首次使用前备份你的数据库
3. **测试先行**: 务必先使用 `dry_run: true` 模式测试
4. **日志监控**: 定期检查日志文件，确保脚本正常运行
5. **网络稳定**: 确保服务器能稳定访问你的New-API服务

## 🐛 故障排除

### 常见错误

1. **401 Unauthorized**
   - 检查Access Token是否正确
   - 检查用户ID是否匹配

2. **404 Not Found**
   - 检查API地址是否正确
   - 检查Token ID是否存在

3. **网络超时**
   - 增加timeout设置
   - 检查网络连接

### 获取帮助

如果遇到问题，请检查：
1. 配置文件格式是否正确
2. 网络连接是否正常
3. API服务是否运行正常
4. 日志文件中的详细错误信息

## 📝 更新日志

- v1.0.0: 初始版本，支持基本的Token余额刷新功能

---

**作者**: Claude 4.0 Sonnet  
**创建时间**: 2025-01-20  
**适用于**: New-API项目
