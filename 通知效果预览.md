# 📱 通知效果预览

## 🎉 成功通知示例

### 全部成功的情况：
```
✅ Token余额刷新成功

📊 执行统计:
• 成功: 3 个
• 失败: 0 个
• 总计: 3 个

📝 处理详情:
✅ Holi (ID: 22)
   📊 昨日用量: $5.08 | 设置后余额: $55.00

✅ 张彪 (ID: 21)
   📊 昨日用量: $0.14 | 设置后余额: $50.00

✨ 刘礼 (ID: 20) [无需更新]
   📊 昨日用量: $0.07 | 设置后余额: $50.00

⏰ 时间: 2025-01-20 02:00:15
```

### 测试模式的情况：
```
✅ Token余额刷新成功

📊 执行统计:
• 成功: 3 个
• 失败: 0 个
• 总计: 3 个

📝 处理详情:
🧪 Holi (ID: 22) [测试模式]
   📊 昨日用量: $5.08 | 设置后余额: $55.00

🧪 张彪 (ID: 21) [测试模式]
   📊 昨日用量: $0.14 | 设置后余额: $50.00

🧪 刘礼 (ID: 20) [测试模式]
   📊 昨日用量: $0.07 | 设置后余额: $50.00

⏰ 时间: 2025-01-20 02:00:15
```

## ❌ 错误通知示例

### 部分失败的情况：
```
❌ Token余额刷新失败

🚨 错误信息: 有 1 个Token刷新失败

📊 执行统计:
• 成功: 2 个
• 失败: 1 个
• 总计: 3 个

📝 详细情况:
✅ Holi (ID: 22)
   📊 昨日用量: $5.08 | 设置后余额: $55.00

✅ 张彪 (ID: 21)
   📊 昨日用量: $0.14 | 设置后余额: $50.00

❌ 刘礼 (ID: 20) - 网络连接超时

⏰ 时间: 2025-01-20 02:00:15
```

## 📊 信息说明

### 状态图标含义：
- ✅ **成功更新**：Token余额已成功刷新到目标值
- ✨ **无需更新**：Token余额已经是目标值，跳过更新
- 🧪 **测试模式**：干运行模式，仅模拟不实际更新
- ❌ **更新失败**：更新过程中出现错误

### 数据含义：
- **昨日用量**：目标余额 - 当前余额 = 昨天实际消耗的金额
- **设置后余额**：执行刷新后的最终余额
- **ID**：Token在系统中的唯一标识

## 🎯 实际使用场景

### 场景1：正常刷新
你的Token昨天消耗了一些额度，脚本会自动补充到目标金额，通知会显示具体消耗了多少。

### 场景2：无消耗
如果某个Token昨天没有使用，余额还是目标值，会显示"无需更新"。

### 场景3：网络问题
如果某个Token因为网络或API问题无法更新，会显示具体的错误信息。

### 场景4：测试模式
在 `dry_run: true` 模式下，所有操作都是模拟的，会显示"测试模式"标识。

## 🔔 通知时机

- **每次脚本执行完成后**自动发送
- **无论成功还是失败**都会通知
- **包含详细的执行统计和每个Token的处理结果**
- **显示执行时间**，方便追踪

这样你就能随时了解Token的使用情况和刷新状态，非常方便！
