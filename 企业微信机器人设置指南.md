# 🤖 企业微信机器人设置指南

## 📋 设置步骤

### 1. 创建企业微信群
- 在企业微信中创建一个群聊
- 或者使用现有的群聊

### 2. 添加群机器人
1. 在群聊中，点击右上角的 `...` 菜单
2. 选择 `群机器人`
3. 点击 `添加机器人`
4. 选择 `自定义机器人`
5. 填写机器人名称，如：`Token刷新通知`
6. 点击 `添加`

### 3. 获取Webhook地址
- 添加成功后，会显示Webhook地址
- 地址格式类似：`https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx`
- **重要**：复制并保存这个地址

### 4. 配置脚本
运行配置脚本：
```bash
python3 setup_notifications.py
```

选择 `1. 企业微信机器人`，然后：
1. 粘贴Webhook地址
2. 设置要@的人员（可以留空表示@所有人）

### 5. 测试通知
在配置脚本中选择 `2. 测试通知`，检查是否能收到测试消息。

## 📱 通知效果预览

### 成功通知示例：
```
✅ Token余额刷新成功

📊 执行统计:
• 成功: 3 个
• 失败: 0 个
• 总计: 3 个

📝 处理详情:
✅ Holi (ID: 22) - 已刷新到 $55
✅ 张彪 (ID: 21) - 已刷新到 $50
✅ 刘礼 (ID: 20) - 已刷新到 $50

⏰ 时间: 2025-01-20 02:00:15
```

### 错误通知示例：
```
❌ Token余额刷新失败

🚨 错误信息: 有 1 个Token刷新失败

📊 执行统计:
• 成功: 2 个
• 失败: 1 个
• 总计: 3 个

⏰ 时间: 2025-01-20 02:00:15
```

## 🔧 高级配置

### @特定人员
如果要@特定人员，可以在配置中设置：
```json
{
  "notifications": {
    "wechat_work": {
      "enabled": true,
      "webhook_url": "你的webhook地址",
      "mentioned_list": ["张三", "李四"]
    }
  }
}
```

### @所有人
```json
{
  "notifications": {
    "wechat_work": {
      "enabled": true,
      "webhook_url": "你的webhook地址", 
      "mentioned_list": ["@all"]
    }
  }
}
```

## ⚠️ 注意事项

1. **Webhook地址安全**：不要泄露Webhook地址，任何人获得此地址都可以向群发送消息
2. **消息频率限制**：企业微信对机器人消息有频率限制，避免过于频繁发送
3. **群成员权限**：只有群成员才能收到@消息
4. **机器人管理**：群管理员可以删除或重新配置机器人

## 🛠️ 故障排除

### 问题1：收不到通知
- 检查Webhook地址是否正确
- 确认机器人是否被删除
- 检查网络连接

### 问题2：@功能不生效
- 确认用户名拼写正确
- 检查用户是否在群中
- 尝试使用 `@all`

### 问题3：通知发送失败
- 查看脚本日志中的错误信息
- 检查企业微信API返回的错误码
- 确认机器人配置是否正确

## 📞 其他通知方式

如果企业微信不适合，还可以选择：
- 钉钉机器人
- Telegram Bot
- 邮件通知

运行 `python3 setup_notifications.py` 可以配置其他通知方式。
