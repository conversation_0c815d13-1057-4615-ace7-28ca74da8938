#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API连接测试脚本
用于测试New-API的连接和认证是否正常

作者: Claude 4.0 Sonnet
创建时间: 2025-01-20
"""

import json
import requests
import sys
from pathlib import Path
from datetime import datetime

def format_currency(system_units, currency_settings=None):
    """
    格式化显示货币金额

    Args:
        system_units: 系统单位数量
        currency_settings: 货币设置，如果为None则使用默认设置

    Returns:
        格式化的货币字符串
    """
    if currency_settings is None:
        # 默认设置：500,000 系统单位 = $1 USD
        currency_settings = {
            "system_units_per_dollar": 500000,
            "currency_symbol": "$"
        }

    try:
        # 确保system_units是数字
        if system_units is None:
            system_units = 0

        system_units = float(system_units)

        if system_units == 0:
            return f"{currency_settings['currency_symbol']}0.00"

        usd_amount = system_units / currency_settings['system_units_per_dollar']
        symbol = currency_settings['currency_symbol']
        return f"{symbol}{usd_amount:.2f}"

    except (ValueError, TypeError, ZeroDivisionError) as e:
        return f"N/A (计算错误: {type(e).__name__})"

def format_timestamp(timestamp):
    """
    格式化时间戳显示

    Args:
        timestamp: 时间戳（可能是Unix时间戳或ISO格式字符串）

    Returns:
        格式化的时间字符串
    """
    if not timestamp or timestamp == 'N/A' or timestamp == 0:
        return 'N/A'

    try:
        # 尝试作为Unix时间戳处理
        if isinstance(timestamp, (int, float)):
            # 检查时间戳是否在合理范围内
            # Unix时间戳应该是正数，且不能太大
            if timestamp < 0:
                return 'N/A (无效时间戳)'
            if timestamp > 2147483647:  # 2038年问题的上限
                return 'N/A (时间戳过大)'

            dt = datetime.fromtimestamp(timestamp)
            return dt.strftime('%Y-%m-%d %H:%M:%S')

        # 尝试作为ISO格式字符串处理
        if isinstance(timestamp, str):
            # 处理可能的ISO格式
            if 'T' in timestamp:
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                return dt.strftime('%Y-%m-%d %H:%M:%S')
            else:
                # 直接返回字符串
                return timestamp

    except (ValueError, TypeError, OSError) as e:
        # 如果无法解析，返回错误信息
        return f'N/A (解析错误: {type(e).__name__})'

    return str(timestamp)

def test_api_connection():
    """测试API连接和认证"""
    
    # 读取配置文件
    config_file = "config.json"
    if not Path(config_file).exists():
        print("❌ 配置文件 config.json 不存在")
        print("请先运行主脚本生成配置文件")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False
    
    # 检查必要配置
    required_fields = ['api_base_url', 'access_token', 'user_id']
    for field in required_fields:
        if field not in config:
            print(f"❌ 配置文件缺少字段: {field}")
            return False

    api_base_url = config['api_base_url']
    access_token = config['access_token']
    user_id = config['user_id']

    # 获取货币设置
    currency_settings = config.get('currency_settings', {
        "system_units_per_dollar": 500000,
        "currency_symbol": "$"
    })
    
    print("🔍 开始测试API连接...")
    print(f"📡 API地址: {api_base_url}")
    print(f"👤 用户ID: {user_id}")
    print(f"🔑 Access Token: {access_token[:20]}...")
    print()

    # 诊断检查
    print("🔧 诊断检查:")
    print(f"   API地址格式: {'✅ 正确' if api_base_url.startswith(('http://', 'https://')) else '❌ 错误'}")
    print(f"   Access Token长度: {len(access_token)} 字符")
    print(f"   用户ID类型: {type(user_id)} ({user_id})")
    print()
    
    # 设置请求头
    headers = {
        'Content-Type': 'application/json',
        'Authorization': access_token,
        'New-Api-User': str(user_id)
    }
    
    # 测试1: 获取用户信息
    print("🧪 测试1: 获取用户信息")
    test_url = f"{api_base_url}/api/user/self"
    print(f"   请求URL: {test_url}")
    print(f"   请求头: {headers}")

    try:
        response = requests.get(
            test_url,
            headers=headers,
            timeout=30,
            verify=True  # 启用SSL验证
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                user_info = data.get('data', {})
                print(f"✅ 用户信息获取成功")
                print(f"   用户名: {user_info.get('username', 'N/A')}")
                print(f"   显示名: {user_info.get('display_name', 'N/A')}")
                print(f"   角色: {user_info.get('role', 'N/A')}")
                print(f"   状态: {user_info.get('status', 'N/A')}")
            else:
                print(f"❌ API返回错误: {data.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
            
    except requests.exceptions.SSLError as e:
        print(f"❌ SSL证书错误: {e}")
        print("💡 建议: 尝试禁用SSL验证或检查证书")
        return False
    except requests.exceptions.ConnectionError as e:
        print(f"❌ 连接错误: {e}")
        print("💡 建议: 检查网络连接和API地址")
        return False
    except requests.exceptions.Timeout as e:
        print(f"❌ 请求超时: {e}")
        print("💡 建议: 增加timeout时间或检查网络")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        print(f"   异常类型: {type(e).__name__}")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        print(f"   错误类型: {type(e).__name__}")
        import traceback
        print(f"   详细信息: {traceback.format_exc()}")
        return False
    
    print()
    
    # 测试2: 获取Token列表并输出所有Token详情
    print("🧪 测试2: 获取Token列表并输出所有Token详情")
    tokens = []  # 用于存储所有token，供测试3使用
    test2_success = False  # 标记测试2是否成功

    try:
        # 获取更多token，设置较大的size值
        response = requests.get(
            f"{api_base_url}/api/token/?p=1&size=100",
            headers=headers,
            timeout=30
        )

        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                tokens_data = data.get('data', {})
                if isinstance(tokens_data, dict):
                    tokens = tokens_data.get('items', [])
                    total = tokens_data.get('total', 0)
                else:
                    tokens = tokens_data if isinstance(tokens_data, list) else []
                    total = len(tokens)

                print(f"✅ Token列表获取成功")
                print(f"   总Token数: {total}")
                print(f"   当前显示: {len(tokens)} 个")
                test2_success = True  # 标记测试2成功

                if tokens:
                    print("   所有Token详情:")
                    for i, token in enumerate(tokens, 1):
                        remain_quota = token.get('remain_quota', 0)
                        print(f"     {i}. Token详细信息:")
                        print(f"        - ID: {token.get('id')}")
                        print(f"        - 名称: {token.get('name', 'N/A')}")
                        print(f"        - Token Key: {token.get('key', 'N/A')}")
                        print(f"        - 余额: {remain_quota} ({format_currency(remain_quota, currency_settings)})")
                        print(f"        - 状态: {token.get('status', 'N/A')}")
                        print(f"        - 分组: {token.get('group', 'default')}")
                        print(f"        - 过期时间: {format_timestamp(token.get('expired_time'))}")
                        print(f"        - 创建时间: {format_timestamp(token.get('created_time'))}")
                        print(f"        - 访问时间: {format_timestamp(token.get('accessed_time'))}")
                        if i < len(tokens):  # 不是最后一个token时添加分隔线
                            print("        " + "-" * 40)
                else:
                    print("   ⚠️  没有找到Token")

            else:
                print(f"❌ API返回错误: {data.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False

    except requests.exceptions.SSLError as e:
        print(f"❌ SSL证书错误: {e}")
        return False
    except requests.exceptions.ConnectionError as e:
        print(f"❌ 连接错误: {e}")
        return False
    except requests.exceptions.Timeout as e:
        print(f"❌ 请求超时: {e}")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        print(f"   异常类型: {type(e).__name__}")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        print(f"   错误类型: {type(e).__name__}")
        return False
    
    print()

    # 测试3: 获取最后一个Token的详细信息
    print("🧪 测试3: 获取最后一个Token的详细信息")
    print(f"   🔍 调试信息: test2_success={test2_success}, tokens数量={len(tokens)}")

    if not test2_success or not tokens:
        print("   ⚠️  测试2未成功或没有找到Token，跳过测试3")
        print("   💡 建议: 检查测试2的错误信息")
    else:
        last_token = tokens[-1]  # 获取最后一个token
        token_id = last_token.get('id')

        print(f"   🎯 目标Token ID: {token_id}")

        if token_id:
            try:
                response = requests.get(
                    f"{api_base_url}/api/token/{token_id}",
                    headers=headers,
                    timeout=30
                )

                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        token_info = data.get('data', {})
                        remain_quota = token_info.get('remain_quota', 0)
                        used_quota = token_info.get('used_quota', 0)
                        print(f"✅ 最后一个Token详细信息获取成功")
                        print(f"   ID: {token_info.get('id')}")
                        print(f"   名称: {token_info.get('name', 'N/A')}")
                        print(f"   Token Key: {token_info.get('key', 'N/A')}")
                        print(f"   当前余额: {remain_quota} ({format_currency(remain_quota, currency_settings)})")
                        print(f"   状态: {token_info.get('status', 'N/A')}")
                        print(f"   分组: {token_info.get('group', 'default')}")
                        print(f"   模型限制启用: {'是' if token_info.get('model_limits_enabled', False) else '否'}")
                        print(f"   模型限制: {token_info.get('model_limits', 'N/A')}")
                        print(f"   允许IP: {token_info.get('allow_ips', 'N/A')}")
                        print(f"   过期时间: {format_timestamp(token_info.get('expired_time'))}")
                        print(f"   创建时间: {format_timestamp(token_info.get('created_time'))}")
                        print(f"   访问时间: {format_timestamp(token_info.get('accessed_time'))}")
                        print(f"   使用次数: {used_quota} ({format_currency(used_quota, currency_settings)})")
                    else:
                        print(f"❌ API返回错误: {data.get('message', '未知错误')}")
                        return False
                else:
                    print(f"❌ HTTP错误: {response.status_code}")
                    return False

            except requests.exceptions.SSLError as e:
                print(f"❌ SSL证书错误: {e}")
                return False
            except requests.exceptions.ConnectionError as e:
                print(f"❌ 连接错误: {e}")
                return False
            except requests.exceptions.Timeout as e:
                print(f"❌ 请求超时: {e}")
                return False
            except requests.exceptions.RequestException as e:
                print(f"❌ 请求异常: {e}")
                print(f"   异常类型: {type(e).__name__}")
                return False
            except Exception as e:
                print(f"❌ 未知错误: {e}")
                print(f"   错误类型: {type(e).__name__}")
                return False
        else:
            print("   ❌ 无法获取最后一个Token的ID")
            return False
    
    print()
    print("🎉 所有测试通过！API连接和认证正常")
    print("✨ 你可以安全地运行主脚本了")
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("🔧 New-API连接测试工具")
    print("=" * 50)
    print()
    
    if test_api_connection():
        sys.exit(0)
    else:
        print()
        print("💡 故障排除建议:")
        print("1. 检查API地址是否正确")
        print("2. 检查Access Token是否有效")
        print("3. 检查用户ID是否正确")
        print("4. 检查网络连接是否正常")
        print("5. 检查New-API服务是否运行正常")
        sys.exit(1)

if __name__ == "__main__":
    main()
