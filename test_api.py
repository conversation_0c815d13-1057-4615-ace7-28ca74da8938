#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API连接测试脚本
用于测试New-API的连接和认证是否正常

作者: Claude 4.0 Sonnet
创建时间: 2025-01-20
"""

import json
import requests
import sys
from pathlib import Path

def test_api_connection():
    """测试API连接和认证"""
    
    # 读取配置文件
    config_file = "config.json"
    if not Path(config_file).exists():
        print("❌ 配置文件 config.json 不存在")
        print("请先运行主脚本生成配置文件")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False
    
    # 检查必要配置
    required_fields = ['api_base_url', 'access_token', 'user_id']
    for field in required_fields:
        if field not in config:
            print(f"❌ 配置文件缺少字段: {field}")
            return False
    
    api_base_url = config['api_base_url']
    access_token = config['access_token']
    user_id = config['user_id']
    
    print("🔍 开始测试API连接...")
    print(f"📡 API地址: {api_base_url}")
    print(f"👤 用户ID: {user_id}")
    print()
    
    # 设置请求头
    headers = {
        'Content-Type': 'application/json',
        'Authorization': access_token,
        'New-Api-User': str(user_id)
    }
    
    # 测试1: 获取用户信息
    print("🧪 测试1: 获取用户信息")
    try:
        response = requests.get(
            f"{api_base_url}/api/user/self",
            headers=headers,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                user_info = data.get('data', {})
                print(f"✅ 用户信息获取成功")
                print(f"   用户名: {user_info.get('username', 'N/A')}")
                print(f"   显示名: {user_info.get('display_name', 'N/A')}")
                print(f"   角色: {user_info.get('role', 'N/A')}")
                print(f"   状态: {user_info.get('status', 'N/A')}")
            else:
                print(f"❌ API返回错误: {data.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False
    
    print()
    
    # 测试2: 获取Token列表
    print("🧪 测试2: 获取Token列表")
    try:
        response = requests.get(
            f"{api_base_url}/api/token/?p=1&size=5",
            headers=headers,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                tokens_data = data.get('data', {})
                if isinstance(tokens_data, dict):
                    tokens = tokens_data.get('items', [])
                    total = tokens_data.get('total', 0)
                else:
                    tokens = tokens_data if isinstance(tokens_data, list) else []
                    total = len(tokens)
                
                print(f"✅ Token列表获取成功")
                print(f"   总Token数: {total}")
                print(f"   当前显示: {len(tokens)} 个")
                
                if tokens:
                    print("   Token详情:")
                    for token in tokens[:3]:  # 只显示前3个
                        print(f"     - ID: {token.get('id')}, 名称: {token.get('name')}, 余额: {token.get('remain_quota', 0)}")
                else:
                    print("   ⚠️  没有找到Token")
                    
            else:
                print(f"❌ API返回错误: {data.get('message', '未知错误')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False
    
    print()
    
    # 测试3: 测试单个Token获取（如果配置中有Token ID）
    if 'tokens_to_refresh' in config and config['tokens_to_refresh']:
        test_token = config['tokens_to_refresh'][0]
        token_id = test_token.get('token_id')
        
        if token_id:
            print(f"🧪 测试3: 获取Token {token_id} 详细信息")
            try:
                response = requests.get(
                    f"{api_base_url}/api/token/{token_id}",
                    headers=headers,
                    timeout=30
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        token_info = data.get('data', {})
                        print(f"✅ Token详细信息获取成功")
                        print(f"   ID: {token_info.get('id')}")
                        print(f"   名称: {token_info.get('name')}")
                        print(f"   当前余额: {token_info.get('remain_quota', 0)}")
                        print(f"   状态: {token_info.get('status')}")
                        print(f"   分组: {token_info.get('group', 'default')}")
                    else:
                        print(f"❌ API返回错误: {data.get('message', '未知错误')}")
                        return False
                else:
                    print(f"❌ HTTP错误: {response.status_code}")
                    return False
                    
            except Exception as e:
                print(f"❌ 请求失败: {e}")
                return False
    
    print()
    print("🎉 所有测试通过！API连接和认证正常")
    print("✨ 你可以安全地运行主脚本了")
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("🔧 New-API连接测试工具")
    print("=" * 50)
    print()
    
    if test_api_connection():
        sys.exit(0)
    else:
        print()
        print("💡 故障排除建议:")
        print("1. 检查API地址是否正确")
        print("2. 检查Access Token是否有效")
        print("3. 检查用户ID是否正确")
        print("4. 检查网络连接是否正常")
        print("5. 检查New-API服务是否运行正常")
        sys.exit(1)

if __name__ == "__main__":
    main()
