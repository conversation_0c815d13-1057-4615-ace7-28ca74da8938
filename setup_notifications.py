#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通知配置设置脚本
帮助用户快速配置各种通知方式
"""

import json
import os
from pathlib import Path

def setup_wechat_work():
    """设置企业微信机器人"""
    print("🤖 设置企业微信机器人通知")
    print("-" * 40)
    print("📋 设置步骤:")
    print("1. 在企业微信群中添加机器人")
    print("2. 复制Webhook地址")
    print("3. 输入到下面的配置中")
    print()
    
    webhook_url = input("请输入企业微信Webhook地址: ").strip()
    if not webhook_url:
        print("❌ Webhook地址不能为空")
        return None
    
    mentioned_list = input("要@的人员(用逗号分隔，留空表示@所有人): ").strip()
    if mentioned_list:
        mentioned_list = [name.strip() for name in mentioned_list.split(',')]
    else:
        mentioned_list = ["@all"]
    
    return {
        "enabled": True,
        "webhook_url": webhook_url,
        "mentioned_list": mentioned_list
    }

def setup_dingtalk():
    """设置钉钉机器人"""
    print("🤖 设置钉钉机器人通知")
    print("-" * 40)
    print("📋 设置步骤:")
    print("1. 在钉钉群中添加自定义机器人")
    print("2. 复制Webhook地址")
    print("3. 输入到下面的配置中")
    print()
    
    webhook_url = input("请输入钉钉Webhook地址: ").strip()
    if not webhook_url:
        print("❌ Webhook地址不能为空")
        return None
    
    at_mobiles = input("要@的手机号(用逗号分隔，留空表示不@任何人): ").strip()
    if at_mobiles:
        at_mobiles = [mobile.strip() for mobile in at_mobiles.split(',')]
    else:
        at_mobiles = []
    
    at_all = input("是否@所有人? (y/n): ").strip().lower() == 'y'
    
    return {
        "enabled": True,
        "webhook_url": webhook_url,
        "at_mobiles": at_mobiles,
        "at_all": at_all
    }

def setup_telegram():
    """设置Telegram机器人"""
    print("🤖 设置Telegram机器人通知")
    print("-" * 40)
    print("📋 设置步骤:")
    print("1. 与@BotFather对话创建机器人")
    print("2. 获取Bot Token")
    print("3. 获取Chat ID (可以发消息给@userinfobot)")
    print()
    
    bot_token = input("请输入Bot Token: ").strip()
    if not bot_token:
        print("❌ Bot Token不能为空")
        return None
    
    chat_id = input("请输入Chat ID: ").strip()
    if not chat_id:
        print("❌ Chat ID不能为空")
        return None
    
    return {
        "enabled": True,
        "bot_token": bot_token,
        "chat_id": chat_id
    }

def update_config_with_notifications():
    """更新配置文件，添加通知配置"""
    config_file = "config.json"
    
    if not Path(config_file).exists():
        print(f"❌ 配置文件 {config_file} 不存在")
        return False
    
    # 读取现有配置
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False
    
    # 初始化通知配置
    if 'notifications' not in config:
        config['notifications'] = {}
    
    print("🔔 通知配置设置")
    print("=" * 50)
    print("支持的通知方式:")
    print("1. 企业微信机器人 (推荐)")
    print("2. 钉钉机器人")
    print("3. Telegram机器人")
    print("4. 跳过通知配置")
    print()
    
    while True:
        choice = input("请选择要配置的通知方式 (1-4): ").strip()
        
        if choice == '1':
            wechat_config = setup_wechat_work()
            if wechat_config:
                config['notifications']['wechat_work'] = wechat_config
                print("✅ 企业微信通知配置完成")
            break
            
        elif choice == '2':
            dingtalk_config = setup_dingtalk()
            if dingtalk_config:
                config['notifications']['dingtalk'] = dingtalk_config
                print("✅ 钉钉通知配置完成")
            break
            
        elif choice == '3':
            telegram_config = setup_telegram()
            if telegram_config:
                config['notifications']['telegram'] = telegram_config
                print("✅ Telegram通知配置完成")
            break
            
        elif choice == '4':
            print("⏭️ 跳过通知配置")
            return True
            
        else:
            print("❌ 无效选择，请输入1-4")
    
    # 保存配置
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        print(f"✅ 配置已保存到 {config_file}")
        return True
    except Exception as e:
        print(f"❌ 保存配置失败: {e}")
        return False

def test_notification():
    """测试通知功能"""
    config_file = "config.json"
    
    if not Path(config_file).exists():
        print(f"❌ 配置文件 {config_file} 不存在")
        return
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return
    
    if 'notifications' not in config:
        print("❌ 未找到通知配置")
        return
    
    # 导入通知管理器
    try:
        from notification_helper import NotificationManager
        
        notifier = NotificationManager(config)
        
        print("📤 发送测试通知...")
        success = notifier.send_notification(
            title="🧪 Token刷新脚本测试通知",
            content="这是一条测试消息，如果你收到了这条消息，说明通知配置成功！\n\n📊 测试信息:\n• 配置文件: config.json\n• 通知功能: 正常",
            status="info"
        )
        
        if success:
            print("✅ 测试通知发送成功！请检查你的通知接收端")
        else:
            print("❌ 测试通知发送失败，请检查配置")
            
    except ImportError:
        print("❌ 无法导入通知模块，请确保 notification_helper.py 文件存在")
    except Exception as e:
        print(f"❌ 测试通知失败: {e}")

def main():
    """主函数"""
    print("🔔 Token刷新脚本通知配置工具")
    print("=" * 50)
    print()
    
    while True:
        print("请选择操作:")
        print("1. 配置通知")
        print("2. 测试通知")
        print("3. 查看当前配置")
        print("4. 退出")
        print()
        
        choice = input("请输入选择 (1-4): ").strip()
        
        if choice == '1':
            update_config_with_notifications()
            
        elif choice == '2':
            test_notification()
            
        elif choice == '3':
            config_file = "config.json"
            if Path(config_file).exists():
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    notifications = config.get('notifications', {})
                    if notifications:
                        print("📋 当前通知配置:")
                        for service, conf in notifications.items():
                            status = "✅ 已启用" if conf.get('enabled', False) else "❌ 已禁用"
                            print(f"  {service}: {status}")
                    else:
                        print("❌ 未配置任何通知")
                except Exception as e:
                    print(f"❌ 读取配置失败: {e}")
            else:
                print("❌ 配置文件不存在")
                
        elif choice == '4':
            print("👋 再见！")
            break
            
        else:
            print("❌ 无效选择，请输入1-4")
        
        print()

if __name__ == "__main__":
    main()
