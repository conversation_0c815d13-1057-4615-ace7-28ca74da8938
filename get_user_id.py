#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速获取用户ID的脚本
"""

import requests
import json

# 你的信息
API_BASE_URL = "https://api.pipidan.xyz"
ACCESS_TOKEN = "NRzUqMnokf3JY/YGKhfSoFTCtRx8sg=="

def get_user_info():
    """获取用户信息来确定用户ID"""
    
    print("🔍 正在尝试获取用户ID...")
    
    # 尝试不同的用户ID（通常是1-1000之间的数字）
    for user_id in range(1, 101):  # 尝试1-100
        headers = {
            'Authorization': ACCESS_TOKEN,
            'New-Api-User': str(user_id),
            'Content-Type': 'application/json'
        }
        
        try:
            response = requests.get(
                f"{API_BASE_URL}/api/user/self",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    user_info = data.get('data', {})
                    print(f"✅ 找到用户ID: {user_id}")
                    print(f"   用户名: {user_info.get('username', 'N/A')}")
                    print(f"   显示名: {user_info.get('display_name', 'N/A')}")
                    print(f"   角色: {user_info.get('role', 'N/A')}")
                    return user_id
                    
        except Exception as e:
            continue
    
    print("❌ 未能自动获取用户ID")
    print("请手动查看浏览器开发者工具中的请求头")
    return None

def test_token_access(user_id):
    """测试Token访问"""
    headers = {
        'Authorization': ACCESS_TOKEN,
        'New-Api-User': str(user_id),
        'Content-Type': 'application/json'
    }
    
    try:
        # 测试获取Token 16的信息
        response = requests.get(
            f"{API_BASE_URL}/api/token/16",
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                token_info = data.get('data', {})
                print(f"✅ Token 16 信息获取成功:")
                print(f"   名称: {token_info.get('name', 'N/A')}")
                print(f"   当前余额: {token_info.get('remain_quota', 0)}")
                print(f"   状态: {token_info.get('status', 'N/A')}")
                return True
            else:
                print(f"❌ Token访问失败: {data.get('message', '未知错误')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
    
    return False

def create_config(user_id):
    """创建配置文件"""
    config = {
        "api_base_url": API_BASE_URL,
        "access_token": ACCESS_TOKEN,
        "user_id": user_id,
        "tokens_to_refresh": [
            {
                "token_id": 16,
                "token_name": "Token-16",
                "target_quota": 50000,
                "description": "每日刷新到50元"
            }
        ],
        "dry_run": True,
        "timeout": 30
    }
    
    with open('config.json', 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 已创建配置文件 config.json")
    print(f"📝 请根据需要修改Token列表和目标余额")

def main():
    print("🚀 New-API用户ID获取工具")
    print("=" * 40)
    
    # 获取用户ID
    user_id = get_user_info()
    
    if user_id:
        print()
        print("🧪 测试Token访问...")
        if test_token_access(user_id):
            print()
            print("📝 创建配置文件...")
            create_config(user_id)
            print()
            print("🎉 设置完成！")
            print("下一步：")
            print("1. 检查并编辑 config.json 文件")
            print("2. 运行: python3 test_api.py")
            print("3. 运行: python3 token_refresh_script.py")
        else:
            print("❌ Token访问测试失败，请检查权限")
    else:
        print()
        print("💡 手动获取用户ID的方法：")
        print("1. 打开浏览器开发者工具(F12)")
        print("2. 切换到Network标签")
        print("3. 刷新页面或访问Token页面")
        print("4. 找到API请求，查看Request Headers")
        print("5. 找到 'New-Api-User: 数字' 这一行")

if __name__ == "__main__":
    main()
