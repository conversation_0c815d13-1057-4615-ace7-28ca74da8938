#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络连接诊断脚本
用于诊断API连接问题
"""

import requests
import json
import socket
import ssl
from urllib.parse import urlparse

def test_basic_connectivity():
    """测试基本网络连接"""
    print("🔍 基本网络连接测试")
    print("-" * 40)
    
    # 读取配置
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        api_url = config['api_base_url']
        access_token = config['access_token']
        user_id = config['user_id']
    except Exception as e:
        print(f"❌ 读取配置失败: {e}")
        return False
    
    print(f"📡 目标API: {api_url}")
    
    # 解析URL
    parsed = urlparse(api_url)
    hostname = parsed.hostname
    port = parsed.port or (443 if parsed.scheme == 'https' else 80)
    
    print(f"🌐 主机名: {hostname}")
    print(f"🔌 端口: {port}")
    print()
    
    # 1. DNS解析测试
    print("1️⃣ DNS解析测试")
    try:
        ip = socket.gethostbyname(hostname)
        print(f"   ✅ DNS解析成功: {hostname} -> {ip}")
    except Exception as e:
        print(f"   ❌ DNS解析失败: {e}")
        return False
    
    # 2. TCP连接测试
    print("2️⃣ TCP连接测试")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex((hostname, port))
        sock.close()
        
        if result == 0:
            print(f"   ✅ TCP连接成功: {hostname}:{port}")
        else:
            print(f"   ❌ TCP连接失败: 错误码 {result}")
            return False
    except Exception as e:
        print(f"   ❌ TCP连接异常: {e}")
        return False
    
    # 3. SSL证书测试（如果是HTTPS）
    if parsed.scheme == 'https':
        print("3️⃣ SSL证书测试")
        try:
            context = ssl.create_default_context()
            with socket.create_connection((hostname, port), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                    cert = ssock.getpeercert()
                    print(f"   ✅ SSL证书有效")
                    print(f"   📋 证书主题: {cert.get('subject', 'N/A')}")
                    print(f"   📅 有效期至: {cert.get('notAfter', 'N/A')}")
        except Exception as e:
            print(f"   ❌ SSL证书问题: {e}")
            print("   💡 建议: 可能需要禁用SSL验证")
    
    # 4. HTTP基础请求测试
    print("4️⃣ HTTP基础请求测试")
    try:
        # 先测试不带认证的请求
        response = requests.get(api_url, timeout=10, verify=True)
        print(f"   ✅ HTTP请求成功: 状态码 {response.status_code}")
    except requests.exceptions.SSLError as e:
        print(f"   ❌ SSL错误: {e}")
        print("   💡 尝试禁用SSL验证...")
        try:
            response = requests.get(api_url, timeout=10, verify=False)
            print(f"   ✅ 禁用SSL验证后成功: 状态码 {response.status_code}")
            print("   ⚠️  建议: 在生产环境中修复SSL证书问题")
        except Exception as e2:
            print(f"   ❌ 仍然失败: {e2}")
            return False
    except Exception as e:
        print(f"   ❌ HTTP请求失败: {e}")
        return False
    
    # 5. API认证测试
    print("5️⃣ API认证测试")
    headers = {
        'Content-Type': 'application/json',
        'Authorization': access_token,
        'New-Api-User': str(user_id)
    }
    
    try:
        test_url = f"{api_url}/api/user/self"
        print(f"   🎯 测试URL: {test_url}")
        print(f"   🔑 认证头: Authorization: {access_token[:20]}...")
        print(f"   👤 用户头: New-Api-User: {user_id}")
        
        response = requests.get(test_url, headers=headers, timeout=10, verify=False)
        print(f"   📊 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("   ✅ API认证成功")
                user_info = data.get('data', {})
                print(f"   👤 用户名: {user_info.get('username', 'N/A')}")
                return True
            else:
                print(f"   ❌ API返回错误: {data.get('message', '未知错误')}")
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
            print(f"   📝 响应内容: {response.text[:200]}...")
            
    except Exception as e:
        print(f"   ❌ API认证测试失败: {e}")
        print(f"   🔍 错误类型: {type(e).__name__}")
        return False
    
    return False

def main():
    print("🔧 New-API网络连接诊断工具")
    print("=" * 50)
    print()
    
    if test_basic_connectivity():
        print()
        print("🎉 网络连接诊断完成 - 连接正常")
        print("✨ 可以尝试运行 test_api.py")
    else:
        print()
        print("❌ 网络连接存在问题")
        print()
        print("💡 常见解决方案:")
        print("1. 检查网络连接是否正常")
        print("2. 检查防火墙设置")
        print("3. 检查API地址是否正确")
        print("4. 检查Access Token是否有效")
        print("5. 如果是SSL问题，联系API提供商")
        print("6. 尝试使用VPN或代理")

if __name__ == "__main__":
    main()
