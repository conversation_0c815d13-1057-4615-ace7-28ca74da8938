#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Token余额自动刷新脚本
每日定时刷新指定Token的余额到设定金额

作者: Claude 4.0 Sonnet
创建时间: 2025-01-20
"""

import json
import requests
import logging
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('token_refresh.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class TokenRefreshManager:
    """Token余额刷新管理器"""
    
    def __init__(self, config_file: str = "config.json"):
        """
        初始化管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.config = self.load_config()
        self.session = requests.Session()
        
        # 设置请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Authorization': self.config['access_token'],
            'New-Api-User': str(self.config['user_id'])
        })
    
    def load_config(self) -> Dict:
        """加载配置文件"""
        config_path = Path(self.config_file)

        if not config_path.exists():
            logger.error(f"配置文件 {self.config_file} 不存在，正在创建示例配置...")
            self.create_example_config()
            sys.exit(1)

        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 验证必要字段
            required_fields = ['api_base_url', 'access_token', 'user_id', 'tokens_to_refresh']
            for field in required_fields:
                if field not in config:
                    raise ValueError(f"配置文件缺少必要字段: {field}")

            # 设置默认货币配置
            if 'currency_settings' not in config:
                config['currency_settings'] = {
                    "system_units_per_dollar": 500000,
                    "currency_symbol": "$",
                    "description": "500000 system units = $1 USD"
                }
                logger.info("使用默认货币设置: 500000 系统单位 = $1 USD")

            logger.info("配置文件加载成功")
            return config

        except (json.JSONDecodeError, ValueError) as e:
            logger.error(f"配置文件格式错误: {e}")
            sys.exit(1)
    
    def create_example_config(self):
        """创建示例配置文件"""
        example_config = {
            "api_base_url": "https://your-domain.com",
            "access_token": "your_access_token_here",
            "user_id": 123,
            "currency_settings": {
                "system_units_per_dollar": 500000,
                "currency_symbol": "$",
                "description": "500000 system units = $1 USD"
            },
            "tokens_to_refresh": [
                {
                    "token_id": 1,
                    "token_name": "测试Token1",
                    "target_amount_usd": 50.0,
                    "description": "每日刷新到$50美元"
                },
                {
                    "token_id": 2,
                    "token_name": "测试Token2",
                    "target_amount_usd": 100.0,
                    "description": "每日刷新到$100美元"
                }
            ],
            "dry_run": True,
            "timeout": 30
        }
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(example_config, f, ensure_ascii=False, indent=2)
        
        logger.info(f"已创建示例配置文件: {self.config_file}")
        logger.info("请编辑配置文件，填入正确的API信息和Token配置")

    def usd_to_system_units(self, usd_amount: float) -> int:
        """
        将美元金额转换为系统单位

        Args:
            usd_amount: 美元金额

        Returns:
            系统单位数量
        """
        units_per_dollar = self.config['currency_settings']['system_units_per_dollar']
        return int(usd_amount * units_per_dollar)

    def system_units_to_usd(self, system_units: int) -> float:
        """
        将系统单位转换为美元金额

        Args:
            system_units: 系统单位数量

        Returns:
            美元金额
        """
        units_per_dollar = self.config['currency_settings']['system_units_per_dollar']
        return system_units / units_per_dollar

    def format_currency(self, system_units: int) -> str:
        """
        格式化显示货币金额

        Args:
            system_units: 系统单位数量

        Returns:
            格式化的货币字符串
        """
        usd_amount = self.system_units_to_usd(system_units)
        symbol = self.config['currency_settings']['currency_symbol']
        return f"{symbol}{usd_amount:.2f}"
    
    def get_token_info(self, token_id: int) -> Optional[Dict]:
        """
        获取Token信息
        
        Args:
            token_id: Token ID
            
        Returns:
            Token信息字典，失败返回None
        """
        url = f"{self.config['api_base_url']}/api/token/{token_id}"
        
        try:
            response = self.session.get(url, timeout=self.config.get('timeout', 30))
            response.raise_for_status()
            
            data = response.json()
            if data.get('success'):
                return data.get('data')
            else:
                logger.error(f"获取Token {token_id} 信息失败: {data.get('message', '未知错误')}")
                return None
                
        except requests.exceptions.RequestException as e:
            logger.error(f"请求Token {token_id} 信息时发生网络错误: {e}")
            return None
    
    def update_token_quota(self, token_info: Dict, target_quota: int) -> bool:
        """
        更新Token余额
        
        Args:
            token_info: Token信息
            target_quota: 目标余额
            
        Returns:
            更新是否成功
        """
        url = f"{self.config['api_base_url']}/api/token/"
        
        # 构建更新数据
        update_data = {
            "id": token_info['id'],
            "name": token_info['name'],
            "expired_time": token_info['expired_time'],
            "remain_quota": target_quota,  # 这是关键字段
            "unlimited_quota": token_info['unlimited_quota'],
            "model_limits_enabled": token_info['model_limits_enabled'],
            "model_limits": token_info['model_limits'],
            "allow_ips": token_info['allow_ips'],
            "group": token_info['group']
        }
        
        try:
            response = self.session.put(url, json=update_data, timeout=self.config.get('timeout', 30))
            response.raise_for_status()
            
            data = response.json()
            if data.get('success'):
                logger.info(f"✅ Token {token_info['name']} (ID: {token_info['id']}) 余额已更新为 {self.format_currency(target_quota)}")
                return True
            else:
                logger.error(f"❌ 更新Token {token_info['id']} 余额失败: {data.get('message', '未知错误')}")
                return False
                
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ 更新Token {token_info['id']} 余额时发生网络错误: {e}")
            return False
    
    def refresh_single_token(self, token_config: Dict) -> bool:
        """
        刷新单个Token的余额

        Args:
            token_config: Token配置信息

        Returns:
            刷新是否成功
        """
        token_id = token_config['token_id']
        token_name = token_config.get('token_name', f'Token-{token_id}')

        # 支持两种配置方式：新的美元配置和旧的系统单位配置
        if 'target_amount_usd' in token_config:
            target_usd = token_config['target_amount_usd']
            target_quota = self.usd_to_system_units(target_usd)
            logger.info(f"🔄 开始处理 {token_name} (ID: {token_id}) - 目标: ${target_usd:.2f}")
        else:
            # 兼容旧配置
            target_quota = token_config['target_quota']
            target_usd = self.system_units_to_usd(target_quota)
            logger.info(f"🔄 开始处理 {token_name} (ID: {token_id}) - 目标: {target_quota} 系统单位")

        # 获取当前Token信息
        token_info = self.get_token_info(token_id)
        if not token_info:
            logger.error(f"❌ 无法获取Token {token_id} 的信息，跳过")
            return False

        current_quota = token_info.get('remain_quota', 0)

        logger.info(f"📊 当前余额: {self.format_currency(current_quota)} ({current_quota} 系统单位)")
        logger.info(f"📊 目标余额: {self.format_currency(target_quota)} ({target_quota} 系统单位)")

        # 检查是否需要更新
        if current_quota == target_quota:
            logger.info(f"✨ Token {token_name} 余额已经是目标值，无需更新")
            return True

        # 干运行模式
        if self.config.get('dry_run', False):
            logger.info(f"🧪 [干运行模式] 将会把 {token_name} 余额从 {self.format_currency(current_quota)} 更新为 {self.format_currency(target_quota)}")
            return True

        # 执行更新
        return self.update_token_quota(token_info, target_quota)
    
    def refresh_all_tokens(self) -> Dict[str, int]:
        """
        刷新所有配置的Token余额
        
        Returns:
            刷新结果统计
        """
        logger.info("🚀 开始执行Token余额刷新任务")
        logger.info(f"⏰ 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        if self.config.get('dry_run', False):
            logger.info("🧪 当前为干运行模式，不会实际更新数据")
        
        tokens_to_refresh = self.config['tokens_to_refresh']
        logger.info(f"📝 共需要处理 {len(tokens_to_refresh)} 个Token")
        
        results = {'success': 0, 'failed': 0, 'total': len(tokens_to_refresh)}
        
        for i, token_config in enumerate(tokens_to_refresh, 1):
            logger.info(f"📍 处理进度: {i}/{results['total']}")
            
            try:
                if self.refresh_single_token(token_config):
                    results['success'] += 1
                else:
                    results['failed'] += 1
            except Exception as e:
                logger.error(f"❌ 处理Token {token_config.get('token_id', 'Unknown')} 时发生异常: {e}")
                results['failed'] += 1
        
        # 输出总结
        logger.info("=" * 50)
        logger.info("📊 刷新任务完成统计:")
        logger.info(f"✅ 成功: {results['success']} 个")
        logger.info(f"❌ 失败: {results['failed']} 个")
        logger.info(f"📝 总计: {results['total']} 个")
        logger.info("=" * 50)
        
        return results

def main():
    """主函数"""
    try:
        manager = TokenRefreshManager()
        results = manager.refresh_all_tokens()
        
        # 根据结果设置退出码
        if results['failed'] > 0:
            sys.exit(1)  # 有失败的情况
        else:
            sys.exit(0)  # 全部成功
            
    except KeyboardInterrupt:
        logger.info("❌ 用户中断执行")
        sys.exit(130)
    except Exception as e:
        logger.error(f"❌ 程序执行出现异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
